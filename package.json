{"name": "retail-sales-pulse-ios", "private": true, "type": "module", "module": "index.ts", "workspaces": ["packages/*"], "scripts": {"build": "cd packages/web && bun run build", "dev": "cd packages/web && bun run dev", "preview": "cd packages/web && bun run preview", "mcp:interactive": "node mcp-interactive.js", "mcp:aug": "node mcp-servers/augment-mcp.js", "mcp:plw": "node mcp-servers/playwright-mcp.js"}, "devDependencies": {"prettier": "^3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-packagejson": "^2.5.15", "prettier-plugin-sh": "^0.17.4", "prettier-plugin-sort-json": "^4.1.1"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"@modelcontextprotocol/inspector": "^0.14.0"}}