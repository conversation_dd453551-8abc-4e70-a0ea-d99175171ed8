# Test info

- Name: Dashboard Fix Tests >> should display fallback content when no data
- Location: /Users/<USER>/Sale bán lẻ/retail-sales-pulse-ios/packages/web/tests/dashboard-fix.test.js:82:3

# Error details

```
Error: expect(received).toContain(expected) // indexOf

Expected substring: "Dashboard"
Received string:    "························
"
    at /Users/<USER>/Sale bán lẻ/retail-sales-pulse-ios/packages/web/tests/dashboard-fix.test.js:97:21
```

# Test source

```ts
   1 | // PLW: Test dashboard fix - kiểm tra giao diện không còn trắng xóa
   2 | import { test, expect } from '@playwright/test';
   3 |
   4 | test.describe('Dashboard Fix Tests', () => {
   5 |   test.beforeEach(async ({ page }) => {
   6 |     // Navigate và auto-login
   7 |     await page.goto('http://localhost:8088');
   8 |     await page.waitForLoadState('networkidle');
   9 |     
   10 |     // Setup test auth
   11 |     await page.evaluate(() => {
   12 |       const testUser = {
   13 |         id: 'test-user-001',
   14 |         name: 'Khổng Đức Mạnh',
   15 |         email: '<EMAIL>',
   16 |         role: 'director',
   17 |         team: 'Phòng Kinh Doanh',
   18 |         location: 'Hà Nội',
   19 |         password_changed: true,
   20 |       };
   21 |       localStorage.setItem('currentUser', JSON.stringify(testUser));
   22 |       localStorage.setItem('authToken', 'test-auth-token');
   23 |       localStorage.setItem('isAuthenticated', 'true');
   24 |     });
   25 |     
   26 |     await page.reload();
   27 |     await page.waitForLoadState('networkidle');
   28 |     await page.waitForTimeout(3000);
   29 |   });
   30 |
   31 |   test('should display dashboard without white screen', async ({ page }) => {
   32 |     console.log('🧪 Testing dashboard displays correctly...');
   33 |     
   34 |     // Navigate to dashboard
   35 |     await page.goto('http://localhost:8088/');
   36 |     await page.waitForLoadState('networkidle');
   37 |     await page.waitForTimeout(3000);
   38 |     
   39 |     // Take screenshot for debugging
   40 |     await page.screenshot({ path: 'test-results/dashboard-fix-check.png', fullPage: true });
   41 |     
   42 |     // Check if page title exists
   43 |     const pageTitle = page.locator('[data-testid="page-title"]');
   44 |     await expect(pageTitle).toBeVisible({ timeout: 10000 });
   45 |     console.log('✅ Page title visible');
   46 |     
   47 |     // Check if dashboard content is visible (not white screen)
   48 |     const dashboardContent = page.locator('.p-4, .p-6');
   49 |     await expect(dashboardContent).toBeVisible();
   50 |     console.log('✅ Dashboard content visible');
   51 |     
   52 |     // Check if at least some content is rendered
   53 |     const bodyContent = await page.locator('body').textContent();
   54 |     expect(bodyContent).toContain('Dashboard');
   55 |     console.log('✅ Dashboard text found in body');
   56 |     
   57 |     // Check if KPI cards or fallback content is visible
   58 |     const kpiSection = page.locator('[data-testid="kpi-cards-grid"], .bg-gradient-to-r');
   59 |     await expect(kpiSection).toBeVisible();
   60 |     console.log('✅ KPI section or fallback visible');
   61 |     
   62 |     console.log('✅ Dashboard fix test passed - no white screen');
   63 |   });
   64 |
   65 |   test('should handle loading state properly', async ({ page }) => {
   66 |     console.log('🧪 Testing loading state handling...');
   67 |     
   68 |     // Navigate to dashboard
   69 |     await page.goto('http://localhost:8088/');
   70 |     
   71 |     // Check for loading indicators or content
   72 |     await page.waitForFunction(() => {
   73 |       const body = document.body.textContent || '';
   74 |       return body.includes('Dashboard') || 
   75 |              body.includes('Đang tải') || 
   76 |              document.querySelector('.animate-pulse') !== null;
   77 |     }, { timeout: 10000 });
   78 |     
   79 |     console.log('✅ Loading state handled properly');
   80 |   });
   81 |
   82 |   test('should display fallback content when no data', async ({ page }) => {
   83 |     console.log('🧪 Testing fallback content display...');
   84 |     
   85 |     // Clear any existing data
   86 |     await page.evaluate(() => {
   87 |       localStorage.removeItem('tasks');
   88 |       localStorage.removeItem('reports');
   89 |     });
   90 |     
   91 |     await page.goto('http://localhost:8088/');
   92 |     await page.waitForLoadState('networkidle');
   93 |     await page.waitForTimeout(3000);
   94 |     
   95 |     // Should show some content even without data
   96 |     const content = await page.locator('body').textContent();
>  97 |     expect(content).toContain('Dashboard');
      |                     ^ Error: expect(received).toContain(expected) // indexOf
   98 |     
   99 |     // Check for either KPI cards or empty state
  100 |     const hasKpiCards = await page.locator('[data-testid="kpi-cards-grid"]').isVisible().catch(() => false);
  101 |     const hasEmptyState = await page.locator('text=Chưa có dữ liệu').isVisible().catch(() => false);
  102 |     const hasFallbackCards = await page.locator('.bg-white\\/95').count() > 0;
  103 |     
  104 |     expect(hasKpiCards || hasEmptyState || hasFallbackCards).toBe(true);
  105 |     console.log('✅ Fallback content displayed properly');
  106 |   });
  107 |
  108 |   test('should display error-free console', async ({ page }) => {
  109 |     console.log('🧪 Testing for console errors...');
  110 |     
  111 |     const consoleErrors = [];
  112 |     page.on('console', msg => {
  113 |       if (msg.type() === 'error') {
  114 |         consoleErrors.push(msg.text());
  115 |       }
  116 |     });
  117 |     
  118 |     await page.goto('http://localhost:8088/');
  119 |     await page.waitForLoadState('networkidle');
  120 |     await page.waitForTimeout(3000);
  121 |     
  122 |     // Filter out known non-critical errors
  123 |     const criticalErrors = consoleErrors.filter(error => 
  124 |       !error.includes('favicon') && 
  125 |       !error.includes('404') &&
  126 |       !error.includes('net::ERR_')
  127 |     );
  128 |     
  129 |     console.log('Console errors found:', criticalErrors);
  130 |     
  131 |     // Should have minimal critical errors
  132 |     expect(criticalErrors.length).toBeLessThan(3);
  133 |     console.log('✅ Console errors within acceptable range');
  134 |   });
  135 |
  136 |   test('should be responsive on different screen sizes', async ({ page }) => {
  137 |     console.log('🧪 Testing responsive design...');
  138 |     
  139 |     await page.goto('http://localhost:8088/');
  140 |     await page.waitForLoadState('networkidle');
  141 |     
  142 |     // Test mobile
  143 |     await page.setViewportSize({ width: 375, height: 667 });
  144 |     await page.waitForTimeout(1000);
  145 |     
  146 |     let content = await page.locator('body').textContent();
  147 |     expect(content).toContain('Dashboard');
  148 |     console.log('✅ Mobile view working');
  149 |     
  150 |     // Test tablet
  151 |     await page.setViewportSize({ width: 768, height: 1024 });
  152 |     await page.waitForTimeout(1000);
  153 |     
  154 |     content = await page.locator('body').textContent();
  155 |     expect(content).toContain('Dashboard');
  156 |     console.log('✅ Tablet view working');
  157 |     
  158 |     // Test desktop
  159 |     await page.setViewportSize({ width: 1920, height: 1080 });
  160 |     await page.waitForTimeout(1000);
  161 |     
  162 |     content = await page.locator('body').textContent();
  163 |     expect(content).toContain('Dashboard');
  164 |     console.log('✅ Desktop view working');
  165 |     
  166 |     console.log('✅ Responsive design test passed');
  167 |   });
  168 |
  169 |   test('should handle different user roles', async ({ page }) => {
  170 |     console.log('🧪 Testing different user roles...');
  171 |     
  172 |     // Test as team leader
  173 |     await page.evaluate(() => {
  174 |       const teamLeaderUser = {
  175 |         id: 'team-leader-001',
  176 |         name: 'Lương Việt Anh',
  177 |         email: '<EMAIL>',
  178 |         role: 'team_leader',
  179 |         team: 'Nhóm 1',
  180 |         location: 'Hà Nội',
  181 |         password_changed: true,
  182 |       };
  183 |       localStorage.setItem('currentUser', JSON.stringify(teamLeaderUser));
  184 |     });
  185 |     
  186 |     await page.reload();
  187 |     await page.waitForLoadState('networkidle');
  188 |     await page.waitForTimeout(2000);
  189 |     
  190 |     let content = await page.locator('body').textContent();
  191 |     expect(content).toContain('Dashboard');
  192 |     console.log('✅ Team leader role working');
  193 |     
  194 |     // Test as employee
  195 |     await page.evaluate(() => {
  196 |       const employeeUser = {
  197 |         id: 'employee-001',
```