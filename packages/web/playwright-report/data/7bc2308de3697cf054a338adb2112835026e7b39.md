# Test info

- Name: Dashboard Fix Tests >> should handle different user roles
- Location: /Users/<USER>/Sale bán lẻ/retail-sales-pulse-ios/packages/web/tests/dashboard-fix.test.js:169:3

# Error details

```
Error: expect(received).toContain(expected) // indexOf

Expected substring: "Dashboard"
Received string:    "························
"
    at /Users/<USER>/Sale bán lẻ/retail-sales-pulse-ios/packages/web/tests/dashboard-fix.test.js:191:21
```

# Test source

```ts
   91 |     await page.goto('http://localhost:8088/');
   92 |     await page.waitForLoadState('networkidle');
   93 |     await page.waitForTimeout(3000);
   94 |     
   95 |     // Should show some content even without data
   96 |     const content = await page.locator('body').textContent();
   97 |     expect(content).toContain('Dashboard');
   98 |     
   99 |     // Check for either KPI cards or empty state
  100 |     const hasKpiCards = await page.locator('[data-testid="kpi-cards-grid"]').isVisible().catch(() => false);
  101 |     const hasEmptyState = await page.locator('text=Chưa có dữ liệu').isVisible().catch(() => false);
  102 |     const hasFallbackCards = await page.locator('.bg-white\\/95').count() > 0;
  103 |     
  104 |     expect(hasKpiCards || hasEmptyState || hasFallbackCards).toBe(true);
  105 |     console.log('✅ Fallback content displayed properly');
  106 |   });
  107 |
  108 |   test('should display error-free console', async ({ page }) => {
  109 |     console.log('🧪 Testing for console errors...');
  110 |     
  111 |     const consoleErrors = [];
  112 |     page.on('console', msg => {
  113 |       if (msg.type() === 'error') {
  114 |         consoleErrors.push(msg.text());
  115 |       }
  116 |     });
  117 |     
  118 |     await page.goto('http://localhost:8088/');
  119 |     await page.waitForLoadState('networkidle');
  120 |     await page.waitForTimeout(3000);
  121 |     
  122 |     // Filter out known non-critical errors
  123 |     const criticalErrors = consoleErrors.filter(error => 
  124 |       !error.includes('favicon') && 
  125 |       !error.includes('404') &&
  126 |       !error.includes('net::ERR_')
  127 |     );
  128 |     
  129 |     console.log('Console errors found:', criticalErrors);
  130 |     
  131 |     // Should have minimal critical errors
  132 |     expect(criticalErrors.length).toBeLessThan(3);
  133 |     console.log('✅ Console errors within acceptable range');
  134 |   });
  135 |
  136 |   test('should be responsive on different screen sizes', async ({ page }) => {
  137 |     console.log('🧪 Testing responsive design...');
  138 |     
  139 |     await page.goto('http://localhost:8088/');
  140 |     await page.waitForLoadState('networkidle');
  141 |     
  142 |     // Test mobile
  143 |     await page.setViewportSize({ width: 375, height: 667 });
  144 |     await page.waitForTimeout(1000);
  145 |     
  146 |     let content = await page.locator('body').textContent();
  147 |     expect(content).toContain('Dashboard');
  148 |     console.log('✅ Mobile view working');
  149 |     
  150 |     // Test tablet
  151 |     await page.setViewportSize({ width: 768, height: 1024 });
  152 |     await page.waitForTimeout(1000);
  153 |     
  154 |     content = await page.locator('body').textContent();
  155 |     expect(content).toContain('Dashboard');
  156 |     console.log('✅ Tablet view working');
  157 |     
  158 |     // Test desktop
  159 |     await page.setViewportSize({ width: 1920, height: 1080 });
  160 |     await page.waitForTimeout(1000);
  161 |     
  162 |     content = await page.locator('body').textContent();
  163 |     expect(content).toContain('Dashboard');
  164 |     console.log('✅ Desktop view working');
  165 |     
  166 |     console.log('✅ Responsive design test passed');
  167 |   });
  168 |
  169 |   test('should handle different user roles', async ({ page }) => {
  170 |     console.log('🧪 Testing different user roles...');
  171 |     
  172 |     // Test as team leader
  173 |     await page.evaluate(() => {
  174 |       const teamLeaderUser = {
  175 |         id: 'team-leader-001',
  176 |         name: 'Lương Việt Anh',
  177 |         email: '<EMAIL>',
  178 |         role: 'team_leader',
  179 |         team: 'Nhóm 1',
  180 |         location: 'Hà Nội',
  181 |         password_changed: true,
  182 |       };
  183 |       localStorage.setItem('currentUser', JSON.stringify(teamLeaderUser));
  184 |     });
  185 |     
  186 |     await page.reload();
  187 |     await page.waitForLoadState('networkidle');
  188 |     await page.waitForTimeout(2000);
  189 |     
  190 |     let content = await page.locator('body').textContent();
> 191 |     expect(content).toContain('Dashboard');
      |                     ^ Error: expect(received).toContain(expected) // indexOf
  192 |     console.log('✅ Team leader role working');
  193 |     
  194 |     // Test as employee
  195 |     await page.evaluate(() => {
  196 |       const employeeUser = {
  197 |         id: 'employee-001',
  198 |         name: 'Nguyễn Văn A',
  199 |         email: '<EMAIL>',
  200 |         role: 'employee',
  201 |         team: 'Nhóm 1',
  202 |         location: 'Hà Nội',
  203 |         password_changed: true,
  204 |       };
  205 |       localStorage.setItem('currentUser', JSON.stringify(employeeUser));
  206 |     });
  207 |     
  208 |     await page.reload();
  209 |     await page.waitForLoadState('networkidle');
  210 |     await page.waitForTimeout(2000);
  211 |     
  212 |     content = await page.locator('body').textContent();
  213 |     expect(content).toContain('Dashboard');
  214 |     console.log('✅ Employee role working');
  215 |     
  216 |     console.log('✅ User roles test passed');
  217 |   });
  218 | });
  219 |
```