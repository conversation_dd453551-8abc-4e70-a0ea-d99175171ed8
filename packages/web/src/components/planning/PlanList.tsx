import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Search, 
  Filter, 
  Calendar, 
  Clock, 
  Users, 
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  CheckCircle,
  AlertCircle,
  PlayCircle
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { User } from '@/types/user';

interface Plan {
  id: string;
  title: string;
  description: string;
  type: 'meeting' | 'site_visit' | 'report' | 'training' | 'other';
  status: 'pending' | 'in_progress' | 'completed' | 'overdue';
  priority: 'high' | 'medium' | 'low';
  startDate: string;
  endDate: string;
  startTime: string;
  endTime: string;
  participants: string[];
  creator: string;
  location?: string;
  notes?: string;
}

interface PlanListProps {
  currentUser: User | null;
}

const PlanList: React.FC<PlanListProps> = ({ currentUser }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');

  // Mock data - sẽ được thay thế bằng dữ liệu thật
  const mockPlans: Plan[] = [
    {
      id: '1',
      title: 'Họp review dự án Q1',
      description: 'Đánh giá tiến độ và kết quả các dự án trong quý 1',
      type: 'meeting',
      status: 'pending',
      priority: 'high',
      startDate: '2025-01-15',
      endDate: '2025-01-15',
      startTime: '09:00',
      endTime: '11:00',
      participants: ['Lương Việt Anh', 'Lê Khánh Duy', 'Phạm Thị Hương'],
      creator: 'Khổng Đức Mạnh',
      location: 'Phòng họp A',
      notes: 'Chuẩn bị báo cáo tiến độ'
    },
    {
      id: '2',
      title: 'Khảo sát địa điểm mới',
      description: 'Khảo sát và đánh giá địa điểm cho dự án mở rộng',
      type: 'site_visit',
      status: 'in_progress',
      priority: 'medium',
      startDate: '2025-01-16',
      endDate: '2025-01-16',
      startTime: '14:00',
      endTime: '17:00',
      participants: ['Phạm Thị Hương', 'Nguyễn Thị Thảo'],
      creator: 'Lương Việt Anh',
      location: 'Quận Cầu Giấy, Hà Nội'
    },
    {
      id: '3',
      title: 'Báo cáo tiến độ tháng',
      description: 'Tổng hợp và báo cáo kết quả kinh doanh tháng',
      type: 'report',
      status: 'completed',
      priority: 'high',
      startDate: '2025-01-10',
      endDate: '2025-01-10',
      startTime: '10:30',
      endTime: '12:00',
      participants: ['Khổng Đức Mạnh'],
      creator: 'Lương Việt Anh',
      location: 'Online'
    },
    {
      id: '4',
      title: 'Đào tạo kỹ năng bán hàng',
      description: 'Khóa đào tạo nâng cao kỹ năng bán hàng cho nhân viên mới',
      type: 'training',
      status: 'overdue',
      priority: 'medium',
      startDate: '2025-01-08',
      endDate: '2025-01-09',
      startTime: '08:30',
      endTime: '17:30',
      participants: ['Lê Tiến Quân', 'Quản Thu Hà'],
      creator: 'Lương Việt Anh',
      location: 'Phòng đào tạo'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 border-green-200';
      case 'in_progress': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'overdue': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4" />;
      case 'in_progress': return <PlayCircle className="w-4 h-4" />;
      case 'pending': return <Clock className="w-4 h-4" />;
      case 'overdue': return <AlertCircle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'Hoàn thành';
      case 'in_progress': return 'Đang thực hiện';
      case 'pending': return 'Chờ thực hiện';
      case 'overdue': return 'Quá hạn';
      default: return 'Không xác định';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'meeting': return '🤝';
      case 'site_visit': return '🏗️';
      case 'report': return '📊';
      case 'training': return '📚';
      default: return '📋';
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'meeting': return 'Họp';
      case 'site_visit': return 'Khảo sát';
      case 'report': return 'Báo cáo';
      case 'training': return 'Đào tạo';
      default: return 'Khác';
    }
  };

  const filteredPlans = mockPlans.filter(plan => {
    const matchesSearch = plan.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         plan.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || plan.status === statusFilter;
    const matchesType = typeFilter === 'all' || plan.type === typeFilter;
    
    return matchesSearch && matchesStatus && matchesType;
  });

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5 text-blue-600" />
            Bộ lọc và tìm kiếm
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Tìm kiếm kế hoạch..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Trạng thái" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả trạng thái</SelectItem>
                <SelectItem value="pending">Chờ thực hiện</SelectItem>
                <SelectItem value="in_progress">Đang thực hiện</SelectItem>
                <SelectItem value="completed">Hoàn thành</SelectItem>
                <SelectItem value="overdue">Quá hạn</SelectItem>
              </SelectContent>
            </Select>

            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Loại kế hoạch" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả loại</SelectItem>
                <SelectItem value="meeting">Họp</SelectItem>
                <SelectItem value="site_visit">Khảo sát</SelectItem>
                <SelectItem value="report">Báo cáo</SelectItem>
                <SelectItem value="training">Đào tạo</SelectItem>
                <SelectItem value="other">Khác</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Plans List */}
      <div className="space-y-4">
        {filteredPlans.map((plan, index) => (
          <motion.div
            key={plan.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="hover:shadow-lg transition-all duration-300 cursor-pointer">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <span className="text-2xl">{getTypeIcon(plan.type)}</span>
                      <h3 className="text-lg font-semibold text-gray-900">{plan.title}</h3>
                      <Badge className={getStatusColor(plan.status)}>
                        {getStatusIcon(plan.status)}
                        <span className="ml-1">{getStatusText(plan.status)}</span>
                      </Badge>
                      <Badge className={getPriorityColor(plan.priority)}>
                        {plan.priority === 'high' ? 'Cao' : plan.priority === 'medium' ? 'Trung bình' : 'Thấp'}
                      </Badge>
                    </div>
                    
                    <p className="text-gray-600 mb-3">{plan.description}</p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-500">
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4" />
                        <span>{plan.startDate} - {plan.endDate}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4" />
                        <span>{plan.startTime} - {plan.endTime}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="w-4 h-4" />
                        <span>{plan.participants.length} người tham gia</span>
                      </div>
                    </div>

                    {plan.location && (
                      <div className="mt-2 text-sm text-gray-500">
                        📍 {plan.location}
                      </div>
                    )}
                  </div>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Eye className="w-4 h-4 mr-2" />
                        Xem chi tiết
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Edit className="w-4 h-4 mr-2" />
                        Chỉnh sửa
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-red-600">
                        <Trash2 className="w-4 h-4 mr-2" />
                        Xóa
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {filteredPlans.length === 0 && (
        <Card className="border-dashed">
          <CardContent className="p-12 text-center">
            <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Không tìm thấy kế hoạch</h3>
            <p className="text-gray-500">Thử thay đổi bộ lọc hoặc tạo kế hoạch mới</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default PlanList;
