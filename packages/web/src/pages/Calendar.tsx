import React, { useState } from 'react';
import { Plus, Calendar as CalendarIcon, List, BarChart3, Settings } from 'lucide-react';

import AppLayout from '@/components/layout/AppLayout';
import PageHeader from '@/components/layout/PageHeader';
import TaskCalendar from '@/components/tasks/TaskCalendar';
import PlanningDashboard from '@/components/planning/PlanningDashboard';
import PlanList from '@/components/planning/PlanList';
import CreatePlanModal from '@/components/planning/CreatePlanModal';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/context/AuthContext';

const Calendar = () => {
  const { currentUser } = useAuth();
  const [activeTab, setActiveTab] = useState('calendar');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleCreatePlan = () => {
    setShowCreateModal(true);
  };

  const handlePlanCreated = () => {
    // Trigger refresh cho các components
    setRefreshTrigger(prev => prev + 1);
  };

  return (
    <AppLayout>
      <PageHeader
        title="Kế hoạch"
        subtitle="Lập và theo dõi kế hoạch công việc, lịch hẹn và mục tiêu"
        actions={
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setActiveTab('dashboard')}
              className="hover:bg-blue-50 hover:border-blue-300 transition-colors"
            >
              <BarChart3 className="w-4 h-4 mr-2" />
              Tổng quan
            </Button>
            <Button
              size="sm"
              onClick={handleCreatePlan}
              className="hover:bg-blue-600 transition-colors"
            >
              <Plus className="w-4 h-4 mr-2" />
              Tạo kế hoạch
            </Button>
          </div>
        }
      />

      <div className="p-4 md:p-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 lg:w-[400px]">
            <TabsTrigger value="calendar" className="flex items-center gap-2">
              <CalendarIcon className="w-4 h-4" />
              <span className="hidden sm:inline">Lịch</span>
            </TabsTrigger>
            <TabsTrigger value="plans" className="flex items-center gap-2">
              <List className="w-4 h-4" />
              <span className="hidden sm:inline">Kế hoạch</span>
            </TabsTrigger>
            <TabsTrigger value="dashboard" className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4" />
              <span className="hidden sm:inline">Tổng quan</span>
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2">
              <Settings className="w-4 h-4" />
              <span className="hidden sm:inline">Cài đặt</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="calendar" className="space-y-6">
            <TaskCalendar />
          </TabsContent>

          <TabsContent value="plans" className="space-y-6">
            <PlanList currentUser={currentUser} key={refreshTrigger} />
          </TabsContent>

          <TabsContent value="dashboard" className="space-y-6">
            <PlanningDashboard currentUser={currentUser} key={refreshTrigger} />
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            <div className="bg-white rounded-xl border border-gray-200 p-6">
              <h3 className="text-lg font-semibold mb-4">Cài đặt kế hoạch</h3>
              <p className="text-gray-600">Tính năng cài đặt sẽ được phát triển trong phiên bản tiếp theo.</p>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Create Plan Modal */}
      <CreatePlanModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        currentUser={currentUser}
        onPlanCreated={handlePlanCreated}
      />
    </AppLayout>
  );
};

export default Calendar;
