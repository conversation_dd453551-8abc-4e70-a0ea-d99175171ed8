import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Plus,
  Grid3X3,
  List,
  Download
} from 'lucide-react';

import AppLayout from '@/components/layout/AppLayout';
import PageHeader from '@/components/layout/PageHeader';
import EmployeeDetailModal from '@/components/employees/EmployeeDetailModal';
import EmployeeStatsCards from '@/components/employees/EmployeeStatsCards';
import EmployeeSearchFilters from '@/components/employees/EmployeeSearchFilters';
import EmployeeCard from '@/components/employees/EmployeeCard';
import EmployeeListView from '@/components/employees/EmployeeListView';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/context/AuthContext';
import { User as UserType } from '@/types/user';

const Employees = () => {
  const { users, teams, currentUser } = useAuth();
  const [selectedLocation, setSelectedLocation] = useState<'all' | 'hanoi' | 'hcm'>('all');
  const [selectedRole, setSelectedRole] = useState<'all' | 'retail_director' | 'team_leader' | 'employee'>('all');
  const [selectedTeam, setSelectedTeam] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedEmployee, setSelectedEmployee] = useState<UserType | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);

  // Permissions check
  const canManageEmployees = currentUser?.name === 'Khổng Đức Mạnh' || currentUser?.role === 'team_leader';

  // Advanced filtering with useMemo for performance
  const filteredUsers = useMemo(() => {
    return users.filter((user) => {
      // Location filter
      if (selectedLocation !== 'all') {
        if (selectedLocation === 'hanoi') {
          if (!(user.location === 'Hà Nội' || user.location === 'hanoi')) return false;
        } else if (selectedLocation === 'hcm') {
          if (!(user.location === 'Hồ Chí Minh' || user.location === 'hcm')) return false;
        } else {
          if (user.location !== selectedLocation) return false;
        }
      }

      // Role filter
      if (selectedRole !== 'all' && user.role !== selectedRole) return false;

      // Team filter
      if (selectedTeam !== 'all' && user.team_id !== selectedTeam) return false;

      // Search filter
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        return (
          user.name.toLowerCase().includes(searchLower) ||
          user.email.toLowerCase().includes(searchLower) ||
          (user.position && user.position.toLowerCase().includes(searchLower))
        );
      }

      return true;
    });
  }, [users, selectedLocation, selectedRole, selectedTeam, searchTerm]);

  // Tìm tên nhóm theo team_id
  const getTeamName = (teamId: string) => {
    if (teamId === '0' || !teamId) return 'Chưa có nhóm';
    const team = teams.find((t) => t.id === teamId);
    return team ? team.name : 'Chưa có nhóm';
  };

  const getRoleName = (role: string) => {
    switch (role) {
      case 'retail_director':
        return 'Trưởng phòng';
      case 'team_leader':
        return 'Trưởng nhóm';
      case 'employee':
        return 'Nhân viên';
      default:
        return role;
    }
  };

  const getLocationName = (location: string) => {
    if (location === 'hanoi' || location === 'Hà Nội') return 'Hà Nội';
    if (location === 'hcm' || location === 'Hồ Chí Minh') return 'Hồ Chí Minh';
    return location;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200';
      case 'inactive': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'Hoạt động';
      case 'inactive': return 'Tạm nghỉ';
      default: return 'Không xác định';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'retail_director': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'team_leader': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'employee': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handleEmployeeAction = (employee: UserType, action: 'view' | 'edit' | 'contact') => {
    switch (action) {
      case 'view':
      case 'edit':
        setSelectedEmployee(employee);
        setShowDetailModal(true);
        break;
      case 'contact':
        if (employee.email) {
          window.open(`mailto:${employee.email}`, '_blank');
        }
        break;
    }
  };

  const handleExportData = () => {
    const exportData = filteredUsers.map(user => ({
      name: user.name,
      email: user.email,
      role: getRoleName(user.role),
      team: getTeamName(user.team_id || ''),
      location: getLocationName(user.location),
      status: getStatusText(user.status)
    }));

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `employees-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <AppLayout>
      <PageHeader
        title="Quản lý nhân viên"
        subtitle={`${filteredUsers.length} nhân viên trong hệ thống`}
        actions={
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={handleExportData}>
              <Download className="w-4 h-4 mr-2" />
              Xuất dữ liệu
            </Button>
            {canManageEmployees && (
              <Button size="sm">
                <Plus className="w-4 h-4 mr-2" />
                Thêm nhân viên
              </Button>
            )}
          </div>
        }
      />

      <div className="p-4 md:p-6 space-y-6">
        {/* Statistics Cards */}
        <EmployeeStatsCards users={users} teams={teams} />

        {/* Filters and Search */}
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <EmployeeSearchFilters
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              selectedLocation={selectedLocation}
              onLocationChange={(value: any) => setSelectedLocation(value)}
              selectedRole={selectedRole}
              onRoleChange={(value: any) => setSelectedRole(value)}
              selectedTeam={selectedTeam}
              onTeamChange={setSelectedTeam}
              teams={teams}
              resultCount={filteredUsers.length}
              totalCount={users.length}
              onExport={handleExportData}
              canExport={canManageEmployees}
            />
          </div>
          <div className="flex items-end gap-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="hover:bg-blue-50 hover:border-blue-300"
            >
              <Grid3X3 className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="hover:bg-blue-50 hover:border-blue-300"
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
        </div>
        {/* Employees Display */}
        {viewMode === 'grid' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <AnimatePresence>
              {filteredUsers.map((user, index) => (
                <EmployeeCard
                  key={user.id}
                  employee={user}
                  index={index}
                  onAction={handleEmployeeAction}
                  canEdit={canManageEmployees}
                  getTeamName={getTeamName}
                  getRoleName={getRoleName}
                  getRoleColor={getRoleColor}
                  getStatusColor={getStatusColor}
                  getStatusText={getStatusText}
                  getLocationName={getLocationName}
                />
              ))}
            </AnimatePresence>
          </div>
        ) : (
          <EmployeeListView
            employees={filteredUsers}
            onAction={handleEmployeeAction}
            canEdit={canManageEmployees}
            getTeamName={getTeamName}
            getRoleName={getRoleName}
            getRoleColor={getRoleColor}
            getStatusColor={getStatusColor}
            getStatusText={getStatusText}
            getLocationName={getLocationName}
          />
        )}

        {/* Empty State */}
        {filteredUsers.length === 0 && (
          <Card className="border-2 border-dashed border-gray-300 bg-gray-50/50">
            <CardContent className="p-12 text-center">
              <div className="text-6xl mb-4">👥</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Không tìm thấy nhân viên</h3>
              <p className="text-gray-500 mb-4">
                {searchTerm || selectedLocation !== 'all' || selectedRole !== 'all' || selectedTeam !== 'all'
                  ? 'Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm'
                  : 'Chưa có nhân viên nào trong hệ thống'}
              </p>
              {canManageEmployees && (
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Thêm nhân viên đầu tiên
                </Button>
              )}
            </CardContent>
          </Card>
        )}

        {/* Teams Section */}
        {filteredUsers.length > 0 && (
          <Tabs defaultValue="teams" className="space-y-6">
            <TabsList className="grid w-full grid-cols-2 lg:w-[400px]">
              <TabsTrigger value="teams">Nhóm làm việc</TabsTrigger>
              <TabsTrigger value="leaders">Trưởng nhóm</TabsTrigger>
            </TabsList>

            <TabsContent value="teams" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {teams
                  .filter((team) => {
                    if (selectedLocation === 'all') return true;
                    if (selectedLocation === 'hanoi') {
                      return team.location === 'Hà Nội' || team.location === 'hanoi';
                    }
                    if (selectedLocation === 'hcm') {
                      return team.location === 'Hồ Chí Minh' || team.location === 'hcm';
                    }
                    return team.location === selectedLocation;
                  })
                  .map((team, index) => {
                    const teamLeader = users.find((user) => user.id === team.leader_id);
                    const teamMembers = users.filter(
                      (user) => user.team_id === team.id && user.id !== team.leader_id,
                    );

                    return (
                      <motion.div
                        key={team.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <Card className="shadow-lg border-0 bg-white/95 backdrop-blur-lg hover:shadow-xl transition-all duration-300">
                          <CardContent className="p-6">
                            <div className="flex items-center justify-between mb-4">
                              <h3 className="font-semibold text-lg text-gray-900">{team.name}</h3>
                              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                                {teamMembers.length + (teamLeader ? 1 : 0)} thành viên
                              </Badge>
                            </div>
                            <p className="text-sm text-gray-600 mb-4">{team.description}</p>

                            {teamLeader && (
                              <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                                <Badge className="bg-blue-600 text-white mb-2">Trưởng nhóm</Badge>
                                <div className="flex items-center gap-3">
                                  <Avatar className="w-10 h-10">
                                    <AvatarImage src={teamLeader.avatar} alt={teamLeader.name} />
                                    <AvatarFallback className="bg-blue-600 text-white">
                                      {teamLeader.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                                    </AvatarFallback>
                                  </Avatar>
                                  <div>
                                    <p className="font-medium text-gray-900">{teamLeader.name}</p>
                                    <p className="text-sm text-gray-600">{teamLeader.email}</p>
                                  </div>
                                </div>
                              </div>
                            )}

                            {teamMembers.length > 0 && (
                              <div>
                                <Badge className="bg-gray-100 text-gray-800 mb-3">Thành viên</Badge>
                                <div className="space-y-2 max-h-32 overflow-y-auto">
                                  {teamMembers.map((member) => (
                                    <div key={member.id} className="flex items-center gap-3 p-2 hover:bg-gray-50 rounded-lg transition-colors">
                                      <Avatar className="w-8 h-8">
                                        <AvatarImage src={member.avatar} alt={member.name} />
                                        <AvatarFallback className="bg-gray-500 text-white text-xs">
                                          {member.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                                        </AvatarFallback>
                                      </Avatar>
                                      <div className="flex-1 min-w-0">
                                        <p className="font-medium text-sm text-gray-900 truncate">{member.name}</p>
                                        <p className="text-xs text-gray-500 truncate">{member.email}</p>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}

                            {teamMembers.length === 0 && !teamLeader && (
                              <p className="text-sm text-gray-500 italic text-center py-4">Chưa có thành viên</p>
                            )}
                          </CardContent>
                        </Card>
                      </motion.div>
                    );
                  })}
              </div>
            </TabsContent>

            <TabsContent value="leaders" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {users
                  .filter(user => user.role === 'team_leader' || user.role === 'retail_director')
                  .map((leader, index) => {
                    const team = teams.find(t => t.leader_id === leader.id);
                    const teamMembers = users.filter(u => u.team_id === team?.id && u.id !== leader.id);

                    return (
                      <motion.div
                        key={leader.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <Card className="shadow-lg border-0 bg-white/95 backdrop-blur-lg hover:shadow-xl transition-all duration-300">
                          <CardContent className="p-6">
                            <div className="flex flex-col items-center text-center">
                              <Avatar className="w-16 h-16 mb-4 ring-2 ring-purple-100">
                                <AvatarImage src={leader.avatar} alt={leader.name} />
                                <AvatarFallback className="bg-gradient-to-br from-purple-500 to-blue-600 text-white text-lg font-semibold">
                                  {leader.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                                </AvatarFallback>
                              </Avatar>

                              <h3 className="font-semibold text-lg text-gray-900 mb-1">{leader.name}</h3>
                              <Badge className={getRoleColor(leader.role)}>
                                {getRoleName(leader.role)}
                              </Badge>

                              {team && (
                                <div className="mt-4 w-full">
                                  <p className="text-sm text-gray-600 mb-2">Quản lý nhóm:</p>
                                  <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                                    {team.name}
                                  </Badge>
                                  <p className="text-xs text-gray-500 mt-2">
                                    {teamMembers.length} thành viên
                                  </p>
                                </div>
                              )}

                              <div className="flex gap-2 mt-4 w-full">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="flex-1"
                                  onClick={() => handleEmployeeAction(leader, 'view')}
                                >
                                  <Eye className="w-4 h-4 mr-1" />
                                  Xem
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleEmployeeAction(leader, 'contact')}
                                >
                                  <Mail className="w-4 h-4" />
                                </Button>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    );
                  })}
              </div>
            </TabsContent>
          </Tabs>
        )}

        {/* Employee Detail Modal */}
        <EmployeeDetailModal
          employee={selectedEmployee}
          isOpen={showDetailModal}
          onClose={() => {
            setShowDetailModal(false);
            setSelectedEmployee(null);
          }}
          canEdit={canManageEmployees}
        />
      </div>
    </AppLayout>
  );
};

export default Employees;
