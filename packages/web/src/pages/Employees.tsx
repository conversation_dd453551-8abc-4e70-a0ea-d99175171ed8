import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Plus,
  Grid3X3,
  List,
  Download,
  Search,
  Eye,
  Edit,
  Mail,
  Phone,
  MapPin,
  Building,
  UserCheck,
  MoreHorizontal
} from 'lucide-react';

import AppLayout from '@/components/layout/AppLayout';
import PageHeader from '@/components/layout/PageHeader';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/context/AuthContext';
import { User as UserType } from '@/types/user';

const Employees = () => {
  const { users, teams, currentUser } = useAuth();
  const [selectedLocation, setSelectedLocation] = useState<'all' | 'hanoi' | 'hcm'>('all');
  const [selectedRole, setSelectedRole] = useState<'all' | 'retail_director' | 'team_leader' | 'employee'>('all');
  const [selectedTeam, setSelectedTeam] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedEmployee, setSelectedEmployee] = useState<UserType | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);

  // Permissions check
  const canManageEmployees = currentUser?.name === 'Khổng Đức Mạnh' || currentUser?.role === 'team_leader';

  // Advanced filtering with useMemo for performance
  const filteredUsers = useMemo(() => {
    return users.filter((user) => {
      // Location filter
      if (selectedLocation !== 'all') {
        if (selectedLocation === 'hanoi') {
          if (!(user.location === 'Hà Nội' || user.location === 'hanoi')) return false;
        } else if (selectedLocation === 'hcm') {
          if (!(user.location === 'Hồ Chí Minh' || user.location === 'hcm')) return false;
        } else {
          if (user.location !== selectedLocation) return false;
        }
      }

      // Role filter
      if (selectedRole !== 'all' && user.role !== selectedRole) return false;

      // Team filter
      if (selectedTeam !== 'all' && user.team_id !== selectedTeam) return false;

      // Search filter
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        return (
          user.name.toLowerCase().includes(searchLower) ||
          user.email.toLowerCase().includes(searchLower) ||
          (user.position && user.position.toLowerCase().includes(searchLower))
        );
      }

      return true;
    });
  }, [users, selectedLocation, selectedRole, selectedTeam, searchTerm]);

  // Tìm tên nhóm theo team_id
  const getTeamName = (teamId: string) => {
    if (teamId === '0' || !teamId) return 'Chưa có nhóm';
    const team = teams.find((t) => t.id === teamId);
    return team ? team.name : 'Chưa có nhóm';
  };

  const getRoleName = (role: string) => {
    switch (role) {
      case 'retail_director':
        return 'Trưởng phòng';
      case 'team_leader':
        return 'Trưởng nhóm';
      case 'employee':
        return 'Nhân viên';
      default:
        return role;
    }
  };

  const getLocationName = (location: string) => {
    if (location === 'hanoi' || location === 'Hà Nội') return 'Hà Nội';
    if (location === 'hcm' || location === 'Hồ Chí Minh') return 'Hồ Chí Minh';
    return location;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200';
      case 'inactive': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'Hoạt động';
      case 'inactive': return 'Tạm nghỉ';
      default: return 'Không xác định';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'retail_director': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'team_leader': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'employee': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handleEmployeeAction = (employee: UserType, action: 'view' | 'edit' | 'contact') => {
    switch (action) {
      case 'view':
      case 'edit':
        setSelectedEmployee(employee);
        setShowDetailModal(true);
        break;
      case 'contact':
        if (employee.email) {
          window.open(`mailto:${employee.email}`, '_blank');
        }
        break;
    }
  };

  const handleExportData = () => {
    const exportData = filteredUsers.map(user => ({
      name: user.name,
      email: user.email,
      role: getRoleName(user.role),
      team: getTeamName(user.team_id || ''),
      location: getLocationName(user.location),
      status: getStatusText(user.status)
    }));

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `employees-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <AppLayout>
      <PageHeader
        title="Quản lý nhân viên"
        subtitle={`${filteredUsers.length} nhân viên trong hệ thống`}
        actions={
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={handleExportData}>
              <Download className="w-4 h-4 mr-2" />
              Xuất dữ liệu
            </Button>
            {canManageEmployees && (
              <Button size="sm">
                <Plus className="w-4 h-4 mr-2" />
                Thêm nhân viên
              </Button>
            )}
          </div>
        }
      />

      <div className="p-4 md:p-6 space-y-6">
        {/* Statistics Cards */}
        {/* <EmployeeStatsCards users={users} teams={teams} /> */}

        {/* Filters and Search */}
        <Card className="shadow-lg border-0 bg-white/95 backdrop-blur-lg">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Tìm kiếm nhân viên..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Location Filter */}
              <Select value={selectedLocation} onValueChange={(value: any) => setSelectedLocation(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Địa điểm" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả địa điểm</SelectItem>
                  <SelectItem value="hanoi">Hà Nội</SelectItem>
                  <SelectItem value="hcm">Hồ Chí Minh</SelectItem>
                </SelectContent>
              </Select>

              {/* Role Filter */}
              <Select value={selectedRole} onValueChange={(value: any) => setSelectedRole(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Vai trò" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả vai trò</SelectItem>
                  <SelectItem value="retail_director">Trưởng phòng</SelectItem>
                  <SelectItem value="team_leader">Trưởng nhóm</SelectItem>
                  <SelectItem value="employee">Nhân viên</SelectItem>
                </SelectContent>
              </Select>

              {/* Team Filter */}
              <Select value={selectedTeam} onValueChange={setSelectedTeam}>
                <SelectTrigger>
                  <SelectValue placeholder="Nhóm" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả nhóm</SelectItem>
                  {teams.map((team) => (
                    <SelectItem key={team.id} value={team.id}>
                      {team.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* View Mode Toggle */}
              <div className="flex items-center gap-2">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid3X3 className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
        {/* Employees Display */}
        {viewMode === 'grid' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <AnimatePresence>
              {filteredUsers.map((user, index) => (
                <motion.div
                  key={user.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ delay: index * 0.05 }}
                >
                  <Card className="shadow-lg border-0 bg-white/95 backdrop-blur-lg hover:shadow-xl transition-all duration-300 cursor-pointer group">
                    <CardContent className="p-6">
                      <div className="flex flex-col items-center text-center">
                        {/* Avatar */}
                        <Avatar className="w-16 h-16 mb-4 ring-2 ring-blue-100 group-hover:ring-blue-300 transition-all">
                          <AvatarImage src={user.avatar} alt={user.name} />
                          <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white text-lg font-semibold">
                            {user.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                          </AvatarFallback>
                        </Avatar>

                        {/* Name and Role */}
                        <h3 className="font-semibold text-lg text-gray-900 mb-1">{user.name}</h3>
                        <Badge className={`${getRoleColor(user.role)} mb-3`}>
                          {getRoleName(user.role)}
                        </Badge>

                        {/* Info */}
                        <div className="space-y-2 w-full text-sm text-gray-600">
                          <div className="flex items-center justify-center gap-2">
                            <Building className="w-4 h-4" />
                            <span>{getTeamName(user.team_id || '')}</span>
                          </div>
                          <div className="flex items-center justify-center gap-2">
                            <MapPin className="w-4 h-4" />
                            <span>{getLocationName(user.location)}</span>
                          </div>
                          <div className="flex items-center justify-center gap-2">
                            <Mail className="w-4 h-4" />
                            <span className="truncate">{user.email}</span>
                          </div>
                        </div>

                        {/* Status */}
                        <div className="mt-4 w-full">
                          <Badge className={`${getStatusColor(user.status)} w-full justify-center`}>
                            <UserCheck className="w-3 h-3 mr-1" />
                            {getStatusText(user.status)}
                          </Badge>
                        </div>

                        {/* Actions */}
                        <div className="flex gap-2 mt-4 w-full">
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-1"
                            onClick={() => handleEmployeeAction(user, 'view')}
                          >
                            <Eye className="w-4 h-4 mr-1" />
                            Xem
                          </Button>
                          {canManageEmployees && (
                            <Button
                              variant="outline"
                              size="sm"
                              className="flex-1"
                              onClick={() => handleEmployeeAction(user, 'edit')}
                            >
                              <Edit className="w-4 h-4 mr-1" />
                              Sửa
                            </Button>
                          )}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEmployeeAction(user, 'contact')}
                          >
                            <Mail className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        ) : (
          <Card className="shadow-lg border-0 bg-white/95 backdrop-blur-lg">
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 border-b">
                    <tr>
                      <th className="text-left p-4 font-semibold text-gray-900">Nhân viên</th>
                      <th className="text-left p-4 font-semibold text-gray-900">Vai trò</th>
                      <th className="text-left p-4 font-semibold text-gray-900">Nhóm</th>
                      <th className="text-left p-4 font-semibold text-gray-900">Địa điểm</th>
                      <th className="text-left p-4 font-semibold text-gray-900">Trạng thái</th>
                      <th className="text-left p-4 font-semibold text-gray-900">Thao tác</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredUsers.map((user, index) => (
                      <motion.tr
                        key={user.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.02 }}
                        className="border-b hover:bg-gray-50 transition-colors"
                      >
                        <td className="p-4">
                          <div className="flex items-center gap-3">
                            <Avatar className="w-10 h-10">
                              <AvatarImage src={user.avatar} alt={user.name} />
                              <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold">
                                {user.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-semibold text-gray-900">{user.name}</p>
                              <p className="text-sm text-gray-500">{user.email}</p>
                            </div>
                          </div>
                        </td>
                        <td className="p-4">
                          <Badge className={getRoleColor(user.role)}>
                            {getRoleName(user.role)}
                          </Badge>
                        </td>
                        <td className="p-4 text-gray-700">{getTeamName(user.team_id || '')}</td>
                        <td className="p-4 text-gray-700">{getLocationName(user.location)}</td>
                        <td className="p-4">
                          <Badge className={getStatusColor(user.status)}>
                            {getStatusText(user.status)}
                          </Badge>
                        </td>
                        <td className="p-4">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="w-4 h-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleEmployeeAction(user, 'view')}>
                                <Eye className="w-4 h-4 mr-2" />
                                Xem chi tiết
                              </DropdownMenuItem>
                              {canManageEmployees && (
                                <DropdownMenuItem onClick={() => handleEmployeeAction(user, 'edit')}>
                                  <Edit className="w-4 h-4 mr-2" />
                                  Chỉnh sửa
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuItem onClick={() => handleEmployeeAction(user, 'contact')}>
                                <Mail className="w-4 h-4 mr-2" />
                                Gửi email
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </td>
                      </motion.tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Empty State */}
        {filteredUsers.length === 0 && (
          <Card className="border-2 border-dashed border-gray-300 bg-gray-50/50">
            <CardContent className="p-12 text-center">
              <div className="text-6xl mb-4">👥</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Không tìm thấy nhân viên</h3>
              <p className="text-gray-500 mb-4">
                {searchTerm || selectedLocation !== 'all' || selectedRole !== 'all' || selectedTeam !== 'all'
                  ? 'Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm'
                  : 'Chưa có nhân viên nào trong hệ thống'}
              </p>
              {canManageEmployees && (
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Thêm nhân viên đầu tiên
                </Button>
              )}
            </CardContent>
          </Card>
        )}

        {/* Teams Section */}
        {filteredUsers.length > 0 && (
          <Tabs defaultValue="teams" className="space-y-6">
            <TabsList className="grid w-full grid-cols-2 lg:w-[400px]">
              <TabsTrigger value="teams">Nhóm làm việc</TabsTrigger>
              <TabsTrigger value="leaders">Trưởng nhóm</TabsTrigger>
            </TabsList>

            <TabsContent value="teams" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {teams
                  .filter((team) => {
                    if (selectedLocation === 'all') return true;
                    if (selectedLocation === 'hanoi') {
                      return team.location === 'Hà Nội' || team.location === 'hanoi';
                    }
                    if (selectedLocation === 'hcm') {
                      return team.location === 'Hồ Chí Minh' || team.location === 'hcm';
                    }
                    return team.location === selectedLocation;
                  })
                  .map((team, index) => {
                    const teamLeader = users.find((user) => user.id === team.leader_id);
                    const teamMembers = users.filter(
                      (user) => user.team_id === team.id && user.id !== team.leader_id,
                    );

                    return (
                      <motion.div
                        key={team.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <Card className="shadow-lg border-0 bg-white/95 backdrop-blur-lg hover:shadow-xl transition-all duration-300">
                          <CardContent className="p-6">
                            <div className="flex items-center justify-between mb-4">
                              <h3 className="font-semibold text-lg text-gray-900">{team.name}</h3>
                              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                                {teamMembers.length + (teamLeader ? 1 : 0)} thành viên
                              </Badge>
                            </div>
                            <p className="text-sm text-gray-600 mb-4">{team.description}</p>

                            {teamLeader && (
                              <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                                <Badge className="bg-blue-600 text-white mb-2">Trưởng nhóm</Badge>
                                <div className="flex items-center gap-3">
                                  <Avatar className="w-10 h-10">
                                    <AvatarImage src={teamLeader.avatar} alt={teamLeader.name} />
                                    <AvatarFallback className="bg-blue-600 text-white">
                                      {teamLeader.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                                    </AvatarFallback>
                                  </Avatar>
                                  <div>
                                    <p className="font-medium text-gray-900">{teamLeader.name}</p>
                                    <p className="text-sm text-gray-600">{teamLeader.email}</p>
                                  </div>
                                </div>
                              </div>
                            )}

                            {teamMembers.length > 0 && (
                              <div>
                                <Badge className="bg-gray-100 text-gray-800 mb-3">Thành viên</Badge>
                                <div className="space-y-2 max-h-32 overflow-y-auto">
                                  {teamMembers.map((member) => (
                                    <div key={member.id} className="flex items-center gap-3 p-2 hover:bg-gray-50 rounded-lg transition-colors">
                                      <Avatar className="w-8 h-8">
                                        <AvatarImage src={member.avatar} alt={member.name} />
                                        <AvatarFallback className="bg-gray-500 text-white text-xs">
                                          {member.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                                        </AvatarFallback>
                                      </Avatar>
                                      <div className="flex-1 min-w-0">
                                        <p className="font-medium text-sm text-gray-900 truncate">{member.name}</p>
                                        <p className="text-xs text-gray-500 truncate">{member.email}</p>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}

                            {teamMembers.length === 0 && !teamLeader && (
                              <p className="text-sm text-gray-500 italic text-center py-4">Chưa có thành viên</p>
                            )}
                          </CardContent>
                        </Card>
                      </motion.div>
                    );
                  })}
              </div>
            </TabsContent>

            <TabsContent value="leaders" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {users
                  .filter(user => user.role === 'team_leader' || user.role === 'retail_director')
                  .map((leader, index) => {
                    const team = teams.find(t => t.leader_id === leader.id);
                    const teamMembers = users.filter(u => u.team_id === team?.id && u.id !== leader.id);

                    return (
                      <motion.div
                        key={leader.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <Card className="shadow-lg border-0 bg-white/95 backdrop-blur-lg hover:shadow-xl transition-all duration-300">
                          <CardContent className="p-6">
                            <div className="flex flex-col items-center text-center">
                              <Avatar className="w-16 h-16 mb-4 ring-2 ring-purple-100">
                                <AvatarImage src={leader.avatar} alt={leader.name} />
                                <AvatarFallback className="bg-gradient-to-br from-purple-500 to-blue-600 text-white text-lg font-semibold">
                                  {leader.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                                </AvatarFallback>
                              </Avatar>

                              <h3 className="font-semibold text-lg text-gray-900 mb-1">{leader.name}</h3>
                              <Badge className={getRoleColor(leader.role)}>
                                {getRoleName(leader.role)}
                              </Badge>

                              {team && (
                                <div className="mt-4 w-full">
                                  <p className="text-sm text-gray-600 mb-2">Quản lý nhóm:</p>
                                  <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                                    {team.name}
                                  </Badge>
                                  <p className="text-xs text-gray-500 mt-2">
                                    {teamMembers.length} thành viên
                                  </p>
                                </div>
                              )}

                              <div className="flex gap-2 mt-4 w-full">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="flex-1"
                                  onClick={() => handleEmployeeAction(leader, 'view')}
                                >
                                  <Eye className="w-4 h-4 mr-1" />
                                  Xem
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleEmployeeAction(leader, 'contact')}
                                >
                                  <Mail className="w-4 h-4" />
                                </Button>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    );
                  })}
              </div>
            </TabsContent>
          </Tabs>
        )}

        {/* Employee Detail Modal */}
        {/* <EmployeeDetailModal
          employee={selectedEmployee}
          isOpen={showDetailModal}
          onClose={() => {
            setShowDetailModal(false);
            setSelectedEmployee(null);
          }}
          canEdit={canManageEmployees}
        /> */}
      </div>
    </AppLayout>
  );
};

export default Employees;
