import React from 'react';

import KpiDashboard from '@/components/dashboard/KpiDashboard';
import SimpleDashboard from '@/components/dashboard/SimpleDashboard';
import AppLayout from '@/components/layout/AppLayout';
import PageHeader from '@/components/layout/PageHeader';
import ErrorBoundary from '@/components/ErrorBoundary';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthContext';
import { useTaskData } from '@/hooks/use-task-data';
import { getDashboardSubtitle } from '@/utils/kpiUtils';
import { dashboardSyncService } from '@/services/DashboardSyncService';

const Index = () => {
  const { currentUser } = useAuth();
  const { tasks, isLoading } = useTaskData();

  // Fallback để tránh màn hình trắng
  let dashboardData;
  let subtitle = 'Tổng quan hiệu suất kinh doanh';

  try {
    // L<PERSON>y dữ liệu dashboard đồng bộ từ tasks và reports
    dashboardData = dashboardSyncService.getSyncedDashboardData(currentUser, tasks);

    // Lấy tiêu đề phụ dựa trên loại người dùng và quyền hạn
    subtitle = getDashboardSubtitle(currentUser);
    const enhancedSubtitle = dashboardData.permissions.canViewAll
      ? `${subtitle} - Toàn phòng ban`
      : dashboardData.permissions.canViewTeam
      ? `${subtitle} - Nhóm ${currentUser?.location}`
      : subtitle;
    subtitle = enhancedSubtitle;

    // Debug logging
    console.log('📊 Dashboard Index - Synced data:', {
      user: currentUser?.name,
      role: currentUser?.role,
      tasksCount: tasks.length,
      kpiCardsCount: dashboardData.kpiCards.length,
      permissions: dashboardData.permissions,
      summary: dashboardData.summary,
      isLoading
    });
  } catch (error) {
    console.error('❌ Dashboard sync error:', error);

    // Fallback data để tránh crash
    dashboardData = {
      kpiCards: [
        {
          title: 'Tổng KTS',
          value: '0',
          oldValue: '0',
          change: 0,
          data: Array(10).fill({ value: 0 }),
          category: 'task' as const
        },
        {
          title: 'Tổng KH/CĐT',
          value: '0',
          oldValue: '0',
          change: 0,
          data: Array(10).fill({ value: 0 }),
          category: 'task' as const
        },
        {
          title: 'Tổng SBG',
          value: '0',
          oldValue: '0',
          change: 0,
          data: Array(10).fill({ value: 0 }),
          category: 'task' as const
        },
        {
          title: 'Tổng Doanh Số',
          value: '0',
          oldValue: '0',
          change: 0,
          data: Array(10).fill({ value: 0 }),
          category: 'sales' as const
        }
      ],
      summary: {
        totalTasks: tasks.length,
        completedTasks: tasks.filter(t => t.status === 'completed').length,
        totalSales: 0,
        completionRate: 0
      },
      permissions: {
        canViewAll: currentUser?.name === 'Khổng Đức Mạnh',
        canViewTeam: currentUser?.role === 'team_leader',
        canViewPersonal: true
      }
    };
  }

  return (
    <AppLayout>
      <PageHeader
        title="Dashboard"
        subtitle={subtitle}
        actions={
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              📊 Báo cáo chi tiết
            </Button>
            <Button size="sm">
              📤 Xuất báo cáo
            </Button>
          </div>
        }
      />

      <ErrorBoundary
        fallback={<SimpleDashboard currentUser={currentUser} isLoading={isLoading} />}
      >
        <KpiDashboard
          kpiData={dashboardData.kpiCards}
          currentUser={currentUser}
          dashboardData={dashboardData}
          isLoading={isLoading}
        />
      </ErrorBoundary>
    </AppLayout>
  );
};

export default Index;
