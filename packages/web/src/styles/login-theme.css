/* Login Theme - <PERSON><PERSON><PERSON> bộ phong cách với trang <PERSON>gin */

/* <PERSON><PERSON><PERSON><PERSON> màu sắc */
:root {
  --login-gradient-start: #ff6b6b;
  --login-gradient-middle: #4ecdc4;
  --login-gradient-end: #6c5ce7;
  --login-text-primary: #2d3436;
  --login-text-secondary: #636e72;
  --login-border: rgba(255, 255, 255, 0.3);
  --login-shadow: 0 10px 25px rgba(108, 92, 231, 0.1);
  --login-hover-shadow: 0 15px 35px rgba(108, 92, 231, 0.15);
}

/* Card styles */
.login-card {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  border: 1px solid var(--login-border);
  box-shadow: var(--login-shadow);
  overflow: hidden;
  transition: all 0.4s ease;
}

.login-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--login-hover-shadow);
}

/* But<PERSON> styles */
.login-button {
  background: linear-gradient(to right, var(--login-gradient-end), #a66efa);
  color: white;
  font-weight: 600;
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  border: none;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(108, 92, 231, 0.2);
}

.login-button:active {
  transform: translateY(0);
  box-shadow: 0 4px 8px rgba(108, 92, 231, 0.1);
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 1.5s ease;
}

.login-button:hover::before {
  left: 100%;
}

/* Input styles */
.login-input {
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(200, 200, 200, 0.3);
  border-radius: 12px;
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
}

.login-input:focus {
  border-color: var(--login-gradient-end);
  box-shadow: 0 0 0 3px rgba(108, 92, 231, 0.15);
  outline: none;
  transform: translateY(-1px);
}

/* Dashboard card styles */
.dashboard-card {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  border: 1px solid var(--login-border);
  box-shadow: var(--login-shadow);
  overflow: hidden;
  transition: all 0.4s ease;
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--login-hover-shadow);
}

/* Background gradient */
.login-gradient-bg {
  background: linear-gradient(
    to bottom right,
    var(--login-gradient-start),
    var(--login-gradient-middle),
    var(--login-gradient-end)
  );
  background-size: 400% 400%;
  animation: gradient-animation 15s ease infinite;
}

/* Text styles */
.login-title {
  color: var(--login-text-primary);
  font-weight: 700;
  font-size: 1.5rem;
  line-height: 1.2;
}

.login-subtitle {
  color: var(--login-text-secondary);
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.4;
}

/* Animation - Chuyển động chậm và mượt mà */
@keyframes gradient-animation {
  0% {
    background-position: 0% 50%;
  }
  25% {
    background-position: 100% 50%;
  }
  50% {
    background-position: 100% 100%;
  }
  75% {
    background-position: 0% 100%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Glass effect */
.login-glass {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.login-glass:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  transform: translateY(-5px);
}

/* Shimmer effect */
.login-shimmer {
  position: relative;
  overflow: hidden;
}

.login-shimmer::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: translateX(-100%);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

/* Animate background pattern */
.bg-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0) translateX(0) rotate(0deg);
  }
  20% {
    transform: translateY(-4px) translateX(4px) rotate(0.5deg);
  }
  40% {
    transform: translateY(-2px) translateX(-2px) rotate(-0.25deg);
  }
  60% {
    transform: translateY(3px) translateX(2px) rotate(0.25deg);
  }
  80% {
    transform: translateY(4px) translateX(-3px) rotate(-0.5deg);
  }
}

.animate-float {
  animation: float 20s ease-in-out infinite;
  will-change: transform;
}

.animate-gradient-animation {
  animation: gradient-animation 15s ease infinite;
}

/* Tăng cường bóng đổ cho text */
.drop-shadow-strong {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7);
}

/* Chuyển đổi mượt mà - optimized for testing */
.opacity-30 {
  opacity: 0.3;
  transition: all 0.2s ease;
}

.opacity-60 {
  opacity: 0.6;
  transition: all 0.15s ease;
}

/* Hiệu ứng cho phòng ban không được chọn */
.department-dimmed {
  opacity: 0.5;
  filter: grayscale(80%) brightness(0.7);
  transition: all 0.5s ease;
}

/* Hiệu ứng cho phòng ban bị khóa */
.department-locked {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
  filter: grayscale(100%) contrast(1.2);
  position: relative;
}

.department-locked::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 10px,
    rgba(255, 255, 255, 0.1) 10px,
    rgba(255, 255, 255, 0.1) 20px
  );
  opacity: 0.2;
  pointer-events: none;
}

/* Hiệu ứng scan line cho locked department */
.scan-line {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #00ff00, transparent);
  animation: scan 3s linear infinite;
}

@keyframes scan {
  0% {
    top: 0;
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    top: 100%;
    opacity: 0;
  }
}

/* Glitch effect cho locked text */
.glitch {
  position: relative;
  animation: glitch 2s infinite;
}

.glitch::before,
.glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch::before {
  animation: glitch-1 0.5s infinite;
  color: #ff0000;
  z-index: -1;
}

.glitch::after {
  animation: glitch-2 0.5s infinite;
  color: #00ff00;
  z-index: -2;
}

@keyframes glitch {
  0%,
  100% {
    transform: translate(0);
  }
  20% {
    transform: translate(-2px, 2px);
  }
  40% {
    transform: translate(-2px, -2px);
  }
  60% {
    transform: translate(2px, 2px);
  }
  80% {
    transform: translate(2px, -2px);
  }
}

@keyframes glitch-1 {
  0%,
  100% {
    transform: translate(0);
  }
  20% {
    transform: translate(-1px, 1px);
  }
  40% {
    transform: translate(-1px, -1px);
  }
  60% {
    transform: translate(1px, 1px);
  }
  80% {
    transform: translate(1px, -1px);
  }
}

@keyframes glitch-2 {
  0%,
  100% {
    transform: translate(0);
  }
  20% {
    transform: translate(1px, -1px);
  }
  40% {
    transform: translate(1px, 1px);
  }
  60% {
    transform: translate(-1px, -1px);
  }
  80% {
    transform: translate(-1px, 1px);
  }
}

/* Fix dropdown layout shift */
.login-form-container {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-form-content {
  position: relative;
  width: 100%;
  max-width: 28rem;
  transform: translateY(0);
  transition: transform 0.3s ease;
}

/* Prevent layout shift when dropdown opens */
[data-radix-select-content] {
  position: fixed !important;
  z-index: 9999 !important;
}

/* Smooth transitions for form elements */
.form-field-container {
  transition: all 0.3s ease;
  will-change: transform;
}

/* Prevent form jumping */
.login-form-wrapper {
  contain: layout;
  transform: translateZ(0);
}

/* Loading screen animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(1);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.5);
    opacity: 1;
  }
}

@keyframes progress {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Loading utilities */
.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
  animation: bounce 1.5s infinite;
}

.animate-progress {
  animation: progress 3s ease-in-out infinite;
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

.animate-scaleIn {
  animation: scaleIn 0.5s ease-out;
}
